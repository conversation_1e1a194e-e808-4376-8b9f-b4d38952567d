const express = require('express');
const cors = require('cors');
const path = require('path');
const LuaMinifier = require('./lua');
const LuaParser = require('./parseLua');
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Obfuscation utility functions
class LuaObfuscator {
    constructor() {
        this.minifier = new LuaMinifier();
        this.parser = new LuaParser();
    }

    // Main obfuscation function with enhanced complexity
    obfuscate(luaCode) {
        try {
            // Reset minifier state for new code
            this.minifier.reset();

            // Step 1: Parse and validate the code
            const parseResult = this.parser.parse(luaCode);
            if (!parseResult.success) {
                return {
                    success: false,
                    error: `Parse error: ${parseResult.errors.map(e => e.message).join(', ')}`
                };
            }

            // Step 2: Minify the code with Roblox-specific optimizations
            const minifiedCode = this.minifier.minify(luaCode, {
                removeComments: true,
                minifyWhitespace: true,
                renameVariables: true,
                renameFunctions: true
            });

            return {
                success: true,
                obfuscated: minifiedCode,
                originalSize: luaCode.length,
                obfuscatedSize: minifiedCode.length,
                analysis: {
                    variables: parseResult.variables.length,
                    functions: parseResult.functions.length,
                    dependencies: parseResult.dependencies.length,
                    warnings: parseResult.warnings.length
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Parse Lua code and return analysis
    analyze(luaCode) {
        try {
            this.parser.reset();
            const result = this.parser.parse(luaCode);

            if (result.success) {
                const validation = this.parser.validate();
                return {
                    success: true,
                    analysis: this.parser.generateReport(),
                    validation: validation
                };
            } else {
                return {
                    success: false,
                    error: result.errors.map(e => e.message).join(', ')
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Create obfuscator instance
const obfuscator = new LuaObfuscator();

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.post('/api/obfuscate', (req, res) => {
    const { code } = req.body;

    if (!code || typeof code !== 'string') {
        return res.status(400).json({
            success: false,
            error: 'Invalid Lua code provided'
        });
    }

    const result = obfuscator.obfuscate(code);
    res.json(result);
});

app.post('/api/analyze', (req, res) => {
    const { code } = req.body;

    if (!code || typeof code !== 'string') {
        return res.status(400).json({
            success: false,
            error: 'Invalid Lua code provided'
        });
    }

    const result = obfuscator.analyze(code);
    res.json(result);
});

// Start server
app.listen(PORT, () => {
    console.log(`Lua Obfuscator server running on http://localhost:${PORT}`);
});

module.exports = app;