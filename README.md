# Lua Obfuscator for Roblox

A powerful web-based Lua code obfuscator and analyzer specifically designed for Roblox development. This tool provides advanced minification, obfuscation, and code analysis capabilities to protect your Lua scripts while maintaining functionality.

## Features

### 🔐 Code Obfuscation
- **Comprehensive Renaming**: Renames ALL user-defined variables and functions to randomized names
- **Advanced Minification**: Removes comments, whitespace, and unnecessary formatting
- **Smart Preservation**: Automatically preserves Roblox globals, built-in functions, and reserved words
- **Multi-Pattern Detection**: Identifies variables in local declarations, function parameters, for loops, and global assignments
- **Regex-Based Processing**: Uses robust pattern matching for reliable identifier detection and replacement
- **High Compression**: Achieves 50-60% size reduction while maintaining full functionality

### 🔍 Code Analysis
- **AST Parsing**: Complete Abstract Syntax Tree analysis
- **Variable Detection**: Identifies all local and global variables
- **Function Analysis**: Maps function declarations and parameters
- **Dependency Tracking**: Detects Roblox API usage and dependencies
- **Code Validation**: Warns about potential issues and naming conflicts
- **Detailed Reports**: Comprehensive analysis with line numbers and statistics

### 🌐 Web Interface
- **Real-time Processing**: Instant obfuscation and analysis
- **Syntax Highlighting**: Code editor with Lua syntax support
- **Statistics Display**: Shows compression ratios and code metrics
- **Copy & Download**: Easy export of processed code
- **Example Code**: Built-in examples for testing

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd lua-obfuscator
```

2. Install dependencies:
```bash
npm install
```

3. Start the server:
```bash
npm start
```

4. Open your browser and navigate to `http://localhost:3000`

## API Endpoints

### POST /api/obfuscate
Obfuscates Lua code with advanced minification and variable renaming.

**Request:**
```json
{
  "code": "-- Your Lua code here\nlocal player = game.Players.LocalPlayer"
}
```

**Response:**
```json
{
  "success": true,
  "obfuscated": "local QzOu=game.Players.LocalPlayer",
  "originalSize": 45,
  "obfuscatedSize": 34,
  "analysis": {
    "variables": 1,
    "functions": 0,
    "dependencies": 2,
    "warnings": 0
  }
}
```

### POST /api/analyze
Analyzes Lua code and provides detailed information about structure and dependencies.

**Request:**
```json
{
  "code": "local player = game.Players.LocalPlayer\nfunction jump()\n  player.Character.Humanoid.Jump = true\nend"
}
```

**Response:**
```json
{
  "success": true,
  "analysis": {
    "summary": {
      "totalVariables": 1,
      "totalFunctions": 1,
      "totalDependencies": 3,
      "errors": 0,
      "warnings": 0
    },
    "variables": [
      {
        "name": "player",
        "type": "local",
        "scope": "global",
        "line": 1,
        "initialized": true
      }
    ],
    "functions": [
      {
        "name": "jump",
        "scope": "global",
        "line": 2,
        "parameters": [],
        "isLocal": false
      }
    ],
    "dependencies": ["game", "game.Players"]
  },
  "validation": {
    "isValid": true,
    "errors": [],
    "warnings": []
  }
}
```

## File Structure

```
lua-obfuscator/
├── lua.js          # Main Lua minifier and obfuscator class
├── parseLua.js     # Lua parser and analyzer class
├── server.js       # Express server with API endpoints
├── test.js         # Test file for functionality verification
├── public/         # Web interface files
│   ├── index.html  # Main HTML interface
│   ├── script.js   # Frontend JavaScript
│   └── styles.css  # CSS styling
└── package.json    # Node.js dependencies
```

## Core Classes

### LuaMinifier (lua.js)
- `minify(code, options)`: Main minification function
- `removeComments(code)`: Strips all comments
- `minifyWhitespace(code)`: Removes unnecessary whitespace
- `renameIdentifiers(ast)`: Renames variables and functions
- `generateVariableName()`: Creates random variable names
- `reset()`: Clears internal state

### LuaParser (parseLua.js)
- `parse(code, options)`: Parses Lua code into AST
- `analyzeAST()`: Extracts variables, functions, and dependencies
- `validate()`: Checks for common issues and conflicts
- `generateReport()`: Creates detailed analysis report
- `isRobloxGlobal(name)`: Identifies Roblox API elements

## Roblox Integration

The obfuscator is specifically designed for Roblox Lua and includes:

- **Protected Globals**: Preserves essential Roblox services and APIs
- **Service Detection**: Identifies game services and their usage
- **API Awareness**: Maintains compatibility with Roblox Studio
- **Instance Handling**: Properly processes Roblox Instance methods
- **Event Connections**: Preserves event handling patterns

## Usage Examples

### Basic Obfuscation
```javascript
const LuaMinifier = require('./lua');
const minifier = new LuaMinifier();

const code = `
local player = game.Players.LocalPlayer
function makePlayerJump()
    player.Character.Humanoid.Jump = true
end
`;

const obfuscated = minifier.minify(code);
console.log(obfuscated);
// Output: local QzOu=game.Players.LocalPlayer function J2Ed()QzOu.Character.Humanoid.Jump=true end
```

### Code Analysis
```javascript
const LuaParser = require('./parseLua');
const parser = new LuaParser();

const result = parser.parse(code);
if (result.success) {
    const report = parser.generateReport();
    console.log('Variables:', report.variables.length);
    console.log('Functions:', report.functions.length);
    console.log('Dependencies:', report.dependencies);
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the ISC License.

## Support

For issues, questions, or contributions, please open an issue on the repository.
