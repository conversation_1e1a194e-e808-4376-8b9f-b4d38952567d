const luaparse = require('luaparse');

/**
 * Advanced <PERSON><PERSON> and Analyzer for Roblox
 * Provides comprehensive parsing, analysis, and transformation capabilities
 */
class LuaParser {
    constructor() {
        this.ast = null;
        this.variables = new Map();
        this.functions = new Map();
        this.dependencies = new Set();
        this.errors = [];
        this.warnings = [];
    }

    /**
     * Parse Lua code into an Abstract Syntax Tree (AST)
     */
    parse(luaCode, options = {}) {
        const opts = {
            locations: true,
            ranges: true,
            comments: true,
            scope: true,
            luaVersion: '5.1',
            ...options
        };

        try {
            this.ast = luaparse.parse(luaCode, opts);
            this.analyzeAST();
            return {
                success: true,
                ast: this.ast,
                variables: Array.from(this.variables.entries()),
                functions: Array.from(this.functions.entries()),
                dependencies: Array.from(this.dependencies),
                errors: this.errors,
                warnings: this.warnings
            };
        } catch (error) {
            this.errors.push({
                type: 'ParseError',
                message: error.message,
                line: error.line || 0,
                column: error.column || 0
            });
            
            return {
                success: false,
                ast: null,
                variables: [],
                functions: [],
                dependencies: [],
                errors: this.errors,
                warnings: this.warnings
            };
        }
    }

    /**
     * Analyze the AST to extract information about variables, functions, and dependencies
     */
    analyzeAST() {
        if (!this.ast) return;

        this.variables.clear();
        this.functions.clear();
        this.dependencies.clear();
        this.errors = [];
        this.warnings = [];

        this.traverseNode(this.ast);
    }

    /**
     * Traverse AST nodes recursively
     */
    traverseNode(node, scope = 'global') {
        if (!node || typeof node !== 'object') return;

        switch (node.type) {
            case 'LocalStatement':
                this.handleLocalStatement(node, scope);
                break;
            case 'AssignmentStatement':
                this.handleAssignmentStatement(node, scope);
                break;
            case 'FunctionDeclaration':
                this.handleFunctionDeclaration(node, scope);
                break;
            case 'CallExpression':
                this.handleCallExpression(node, scope);
                break;
            case 'MemberExpression':
                this.handleMemberExpression(node, scope);
                break;
            case 'Identifier':
                this.handleIdentifier(node, scope);
                break;
        }

        // Recursively traverse child nodes
        for (const key in node) {
            if (key !== 'type' && key !== 'name') {
                if (Array.isArray(node[key])) {
                    node[key].forEach(child => this.traverseNode(child, scope));
                } else if (typeof node[key] === 'object') {
                    this.traverseNode(node[key], scope);
                }
            }
        }
    }

    /**
     * Handle local variable statements
     */
    handleLocalStatement(node, scope) {
        if (node.variables) {
            node.variables.forEach((variable, index) => {
                if (variable.type === 'Identifier') {
                    const varInfo = {
                        name: variable.name,
                        type: 'local',
                        scope: scope,
                        line: variable.loc ? variable.loc.start.line : 0,
                        initialized: node.init && node.init[index] ? true : false,
                        initValue: node.init && node.init[index] ? this.getNodeValue(node.init[index]) : null
                    };
                    this.variables.set(`${scope}:${variable.name}`, varInfo);
                }
            });
        }
    }

    /**
     * Handle assignment statements
     */
    handleAssignmentStatement(node, scope) {
        if (node.variables) {
            node.variables.forEach((variable, index) => {
                if (variable.type === 'Identifier') {
                    const varInfo = {
                        name: variable.name,
                        type: 'assignment',
                        scope: scope,
                        line: variable.loc ? variable.loc.start.line : 0,
                        initialized: true,
                        initValue: node.init && node.init[index] ? this.getNodeValue(node.init[index]) : null
                    };
                    this.variables.set(`${scope}:${variable.name}`, varInfo);
                }
            });
        }
    }

    /**
     * Handle function declarations
     */
    handleFunctionDeclaration(node, scope) {
        if (node.identifier && node.identifier.type === 'Identifier') {
            const funcInfo = {
                name: node.identifier.name,
                scope: scope,
                line: node.loc ? node.loc.start.line : 0,
                parameters: node.parameters ? node.parameters.map(p => p.name) : [],
                isLocal: node.isLocal || false,
                bodyLines: node.loc ? (node.loc.end.line - node.loc.start.line + 1) : 0
            };
            this.functions.set(`${scope}:${node.identifier.name}`, funcInfo);
        }
    }

    /**
     * Handle function calls
     */
    handleCallExpression(node, scope) {
        if (node.base && node.base.type === 'Identifier') {
            // Check for Roblox-specific function calls
            const funcName = node.base.name;
            if (this.isRobloxFunction(funcName)) {
                this.dependencies.add(funcName);
            }
        } else if (node.base && node.base.type === 'MemberExpression') {
            // Handle method calls like game:GetService()
            this.handleMemberExpression(node.base, scope);
        }
    }

    /**
     * Handle member expressions (property access)
     */
    handleMemberExpression(node, scope) {
        if (node.base && node.base.type === 'Identifier') {
            const baseName = node.base.name;
            if (this.isRobloxGlobal(baseName)) {
                this.dependencies.add(baseName);
                
                // Track specific services
                if (node.identifier && node.identifier.type === 'Identifier') {
                    const memberName = node.identifier.name;
                    this.dependencies.add(`${baseName}.${memberName}`);
                }
            }
        }
    }

    /**
     * Handle identifier references
     */
    handleIdentifier(node, scope) {
        if (this.isRobloxGlobal(node.name)) {
            this.dependencies.add(node.name);
        }
    }

    /**
     * Get the value of a node (for simple literals)
     */
    getNodeValue(node) {
        if (!node) return null;
        
        switch (node.type) {
            case 'StringLiteral':
                return node.value;
            case 'NumericLiteral':
                return node.value;
            case 'BooleanLiteral':
                return node.value;
            case 'NilLiteral':
                return null;
            default:
                return `<${node.type}>`;
        }
    }

    /**
     * Check if a function name is a Roblox built-in function
     */
    isRobloxFunction(name) {
        const robloxFunctions = [
            'wait', 'spawn', 'delay', 'tick', 'print', 'warn', 'error',
            'assert', 'type', 'typeof', 'getfenv', 'setfenv', 'getmetatable',
            'setmetatable', 'rawget', 'rawset', 'rawequal', 'rawlen',
            'next', 'pairs', 'ipairs', 'select', 'tonumber', 'tostring',
            'pcall', 'xpcall', 'loadstring'
        ];
        return robloxFunctions.includes(name);
    }

    /**
     * Check if an identifier is a Roblox global
     */
    isRobloxGlobal(name) {
        const robloxGlobals = [
            'game', 'workspace', 'script', 'Instance', 'Vector3', 'Vector2',
            'CFrame', 'UDim2', 'Color3', 'BrickColor', 'Ray', 'Region3',
            'Enum', 'UserSettings', '_G', 'shared'
        ];
        return robloxGlobals.includes(name);
    }

    /**
     * Generate a detailed analysis report
     */
    generateReport() {
        const report = {
            summary: {
                totalVariables: this.variables.size,
                totalFunctions: this.functions.size,
                totalDependencies: this.dependencies.size,
                errors: this.errors.length,
                warnings: this.warnings.length
            },
            variables: Array.from(this.variables.values()),
            functions: Array.from(this.functions.values()),
            dependencies: Array.from(this.dependencies),
            errors: this.errors,
            warnings: this.warnings
        };

        return report;
    }

    /**
     * Validate the parsed code for common issues
     */
    validate() {
        this.warnings = [];

        // Check for unused variables
        this.variables.forEach((varInfo, key) => {
            if (!varInfo.initialized) {
                this.warnings.push({
                    type: 'UnusedVariable',
                    message: `Variable '${varInfo.name}' is declared but never initialized`,
                    line: varInfo.line,
                    severity: 'warning'
                });
            }
        });

        // Check for potential naming conflicts with Roblox globals
        this.variables.forEach((varInfo, key) => {
            if (this.isRobloxGlobal(varInfo.name)) {
                this.warnings.push({
                    type: 'NamingConflict',
                    message: `Variable '${varInfo.name}' conflicts with Roblox global`,
                    line: varInfo.line,
                    severity: 'warning'
                });
            }
        });

        return {
            isValid: this.errors.length === 0,
            errors: this.errors,
            warnings: this.warnings
        };
    }

    /**
     * Reset parser state
     */
    reset() {
        this.ast = null;
        this.variables.clear();
        this.functions.clear();
        this.dependencies.clear();
        this.errors = [];
        this.warnings = [];
    }
}

module.exports = LuaParser;
