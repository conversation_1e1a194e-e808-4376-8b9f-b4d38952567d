<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lua Script Obfuscator</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔒 Lua Script Obfuscator</h1>
            <p>Transform your Lua scripts into heavily obfuscated code that's nearly impossible to reverse engineer</p>
        </header>

        <main>
            <div class="editor-section">
                <div class="input-panel">
                    <div class="panel-header">
                        <h3>📝 Original Lua Code</h3>
                        <div class="panel-actions">
                            <button id="clearInput" class="btn btn-secondary">Clear</button>
                            <button id="loadExample" class="btn btn-secondary">Load Example</button>
                        </div>
                    </div>
                    <textarea 
                        id="inputCode" 
                        placeholder="-- Enter your Lua code here
print('Hello, World!')
local x = 10
local y = 20
print('Sum:', x + y)"
                        spellcheck="false"
                    ></textarea>
                    <div class="input-info">
                        <span id="inputStats">Lines: 0 | Characters: 0</span>
                    </div>
                </div>

                <div class="controls">
                    <button id="analyzeBtn" class="btn btn-secondary">
                        <span class="btn-text">🔍 Analyze Code</span>
                        <span class="btn-loading" style="display: none;">⏳ Analyzing...</span>
                    </button>
                    <button id="obfuscateBtn" class="btn btn-primary">
                        <span class="btn-text">🔐 Obfuscate Code</span>
                        <span class="btn-loading" style="display: none;">⏳ Processing...</span>
                    </button>
                </div>

                <div class="output-panel">
                    <div class="panel-header">
                        <h3>🛡️ Obfuscated Output</h3>
                        <div class="panel-actions">
                            <button id="copyOutput" class="btn btn-success" disabled>📋 Copy</button>
                            <button id="downloadOutput" class="btn btn-secondary" disabled>💾 Download</button>
                        </div>
                    </div>
                    <textarea 
                        id="outputCode" 
                        readonly 
                        placeholder="Obfuscated code will appear here..."
                    ></textarea>
                    <div class="output-info">
                        <span id="outputStats">Ready to obfuscate</span>
                        <span id="compressionRatio"></span>
                    </div>
                </div>
            </div>

            <div class="features-section">
                <h3>🔧 Obfuscation Features</h3>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🔐</div>
                        <h4>Multi-Layer Encryption</h4>
                        <p>Base64 encoding + XOR encryption with random keys</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🌀</div>
                        <h4>Complex Patterns</h4>
                        <p>Difficult-to-understand code patterns and structures</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🗜️</div>
                        <h4>Minification</h4>
                        <p>Removes comments, whitespace, and unnecessary characters</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h4>Embedded Decoder</h4>
                        <p>Self-contained decoder within the obfuscated output</p>
                    </div>
                </div>
            </div>

            <div class="warning-section">
                <div class="warning-box">
                    <h4>⚠️ Important Notes</h4>
                    <ul>
                        <li>Always test your obfuscated code before deployment</li>
                        <li>Keep a backup of your original source code</li>
                        <li>Obfuscation may slightly impact performance</li>
                        <li>This tool is for legitimate code protection purposes only</li>
                    </ul>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2024 Lua Obfuscator Tool | Built for secure code protection</p>
        </footer>
    </div>

    <div id="notification" class="notification"></div>

    <script src="script.js"></script>
</body>
</html>
