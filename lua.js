const luaparse = require('luaparse');

/**
 * Lua Minifier and Obfuscator for Roblox
 * Provides advanced minification and obfuscation techniques
 */
class LuaMinifier {
    constructor() {
        this.variableCounter = 0;
        this.functionCounter = 0;
        this.variableMap = new Map();
        this.functionMap = new Map();
        this.reservedWords = new Set([
            'and', 'break', 'do', 'else', 'elseif', 'end', 'false', 'for',
            'function', 'if', 'in', 'local', 'nil', 'not', 'or', 'repeat',
            'return', 'then', 'true', 'until', 'while', 'goto'
        ]);
        this.robloxGlobals = new Set([
            'game', 'workspace', 'script', 'wait', 'spawn', 'delay', 'tick',
            'print', 'warn', 'error', 'assert', 'type', 'typeof', 'getfenv',
            'setfenv', 'getmetatable', 'setmetatable', 'rawget', 'rawset',
            'rawequal', 'rawlen', 'next', 'pairs', 'ipairs', 'select',
            'tonumber', 'tostring', 'pcall', 'xpcall', 'loadstring',
            'Instance', 'Vector3', 'Vector2', 'CFrame', 'UDim2', 'Color3',
            'BrickColor', 'Ray', 'Region3', 'Enum', 'UserSettings'
        ]);
    }

    /**
     * Generate a random variable name
     */
    generateVariableName() {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let name;
        do {
            name = '';
            const length = Math.floor(Math.random() * 3) + 1; // 1-3 characters
            for (let i = 0; i < length; i++) {
                name += chars[Math.floor(Math.random() * chars.length)];
            }
        } while (this.reservedWords.has(name) || this.robloxGlobals.has(name));
        return name;
    }

    /**
     * Generate a random function name
     */
    generateFunctionName() {
        return this.generateVariableName();
    }

    /**
     * Remove all comments from Lua code
     */
    removeComments(code) {
        // Remove single-line comments
        code = code.replace(/--(?!\[\[).*$/gm, '');

        // Remove multi-line comments
        code = code.replace(/--\[\[[\s\S]*?\]\]/g, '');

        return code;
    }

    /**
     * Minify whitespace and formatting
     */
    minifyWhitespace(code) {
        // Remove extra whitespace but preserve necessary spaces
        code = code.replace(/\s+/g, ' ');

        // Remove spaces around operators where safe
        code = code.replace(/\s*([+\-*/%=<>~!&|^(){}[\],;:])\s*/g, '$1');

        // Remove spaces after keywords where safe
        code = code.replace(/\b(local|function|if|then|else|elseif|end|for|while|do|repeat|until|return)\s+/g, '$1 ');

        // Clean up multiple spaces
        code = code.replace(/\s+/g, ' ');

        // Remove leading/trailing whitespace
        code = code.trim();

        return code;
    }

    /**
     * Rename variables and functions
     */
    renameIdentifiers(ast) {
        const self = this;

        function traverse(node, scope = new Set()) {
            if (!node || typeof node !== 'object') return;

            if (node.type === 'LocalStatement') {
                // Handle local variable declarations
                if (node.variables) {
                    node.variables.forEach(variable => {
                        if (variable.type === 'Identifier' && !self.robloxGlobals.has(variable.name)) {
                            const newName = self.generateVariableName();
                            self.variableMap.set(variable.name, newName);
                            variable.name = newName;
                            scope.add(newName);
                        }
                    });
                }
            } else if (node.type === 'FunctionDeclaration') {
                // Handle function declarations
                if (node.identifier && node.identifier.type === 'Identifier' && !self.robloxGlobals.has(node.identifier.name)) {
                    const newName = self.generateFunctionName();
                    self.functionMap.set(node.identifier.name, newName);
                    node.identifier.name = newName;
                }

                // Handle function parameters
                if (node.parameters) {
                    node.parameters.forEach(param => {
                        if (param.type === 'Identifier') {
                            const newName = self.generateVariableName();
                            self.variableMap.set(param.name, newName);
                            param.name = newName;
                        }
                    });
                }
            } else if (node.type === 'Identifier') {
                // Rename identifier references
                if (self.variableMap.has(node.name)) {
                    node.name = self.variableMap.get(node.name);
                } else if (self.functionMap.has(node.name)) {
                    node.name = self.functionMap.get(node.name);
                }
            }

            // Recursively traverse child nodes
            for (const key in node) {
                if (key !== 'type' && key !== 'name') {
                    if (Array.isArray(node[key])) {
                        node[key].forEach(child => traverse(child, new Set(scope)));
                    } else if (typeof node[key] === 'object') {
                        traverse(node[key], new Set(scope));
                    }
                }
            }
        }

        traverse(ast);
        return ast;
    }

    /**
     * Convert AST back to Lua code
     */
    astToCode(ast) {
        function nodeToString(node) {
            if (!node) return '';

            switch (node.type) {
                case 'Chunk':
                    return node.body.map(nodeToString).join(' ');

                case 'LocalStatement':
                    const vars = node.variables.map(v => nodeToString(v)).join(',');
                    const init = node.init && node.init.length > 0 ? '=' + node.init.map(nodeToString).join(',') : '';
                    return `local ${vars}${init} `;

                case 'AssignmentStatement':
                    const assignVars = node.variables.map(v => nodeToString(v)).join(',');
                    const assignInit = node.init.map(nodeToString).join(',');
                    return `${assignVars}=${assignInit} `;

                case 'FunctionDeclaration':
                    const funcType = node.isLocal ? 'local function' : 'function';
                    const name = node.identifier ? nodeToString(node.identifier) : '';
                    const params = node.parameters ? node.parameters.map(p => nodeToString(p)).join(',') : '';
                    const body = node.body.map(nodeToString).join(' ');
                    return `${funcType} ${name}(${params}) ${body}end `;

                case 'CallStatement':
                    return nodeToString(node.expression) + ' ';

                case 'CallExpression':
                    const base = nodeToString(node.base);
                    const args = node.arguments.map(nodeToString).join(',');
                    return `${base}(${args})`;

                case 'MemberExpression':
                    const memberBase = nodeToString(node.base);
                    const identifier = nodeToString(node.identifier);
                    const indexer = node.indexer === '.' ? '.' : ':';
                    return `${memberBase}${indexer}${identifier}`;

                case 'IndexExpression':
                    const indexBase = nodeToString(node.base);
                    const index = nodeToString(node.index);
                    return `${indexBase}[${index}]`;

                case 'IfStatement':
                    let ifStr = `if ${nodeToString(node.condition)} then ${node.body.map(nodeToString).join(' ')}`;
                    if (node.elseifs) {
                        node.elseifs.forEach(elseif => {
                            ifStr += `elseif ${nodeToString(elseif.condition)} then ${elseif.body.map(nodeToString).join(' ')}`;
                        });
                    }
                    if (node.else) {
                        ifStr += `else ${node.else.map(nodeToString).join(' ')}`;
                    }
                    ifStr += 'end ';
                    return ifStr;

                case 'WhileStatement':
                    return `while ${nodeToString(node.condition)} do ${node.body.map(nodeToString).join(' ')}end `;

                case 'ForNumericStatement':
                    const variable = nodeToString(node.variable);
                    const start = nodeToString(node.start);
                    const end = nodeToString(node.end);
                    const step = node.step ? ',' + nodeToString(node.step) : '';
                    const forBody = node.body.map(nodeToString).join(' ');
                    return `for ${variable}=${start},${end}${step} do ${forBody}end `;

                case 'ReturnStatement':
                    const returnArgs = node.arguments ? node.arguments.map(nodeToString).join(',') : '';
                    return `return ${returnArgs} `;

                case 'Identifier':
                    return node.name;

                case 'StringLiteral':
                    const stringValue = node.value || '';
                    return `"${stringValue.toString().replace(/"/g, '\\"')}"`;

                case 'NumericLiteral':
                    return (node.value || 0).toString();

                case 'BooleanLiteral':
                    return node.value.toString();

                case 'NilLiteral':
                    return 'nil';

                case 'BinaryExpression':
                    const left = nodeToString(node.left);
                    const right = nodeToString(node.right);
                    return `${left}${node.operator}${right}`;

                case 'UnaryExpression':
                    const argument = nodeToString(node.argument);
                    return `${node.operator}${argument}`;

                case 'TableConstructorExpression':
                    const fields = node.fields ? node.fields.map(nodeToString).join(',') : '';
                    return `{${fields}}`;

                case 'TableKey':
                    const key = nodeToString(node.key);
                    const value = nodeToString(node.value);
                    return `[${key}]=${value}`;

                case 'TableKeyString':
                    const keyStr = nodeToString(node.key);
                    const valueStr = nodeToString(node.value);
                    return `${keyStr}=${valueStr}`;

                case 'TableValue':
                    return nodeToString(node.value);

                default:
                    // For other node types, try to reconstruct basic structure
                    if (node.body && Array.isArray(node.body)) {
                        return node.body.map(nodeToString).join(' ');
                    }
                    return '';
            }
        }

        return nodeToString(ast).trim();
    }

    /**
     * Main minify function for Roblox Lua
     */
    minify(luaCode, options = {}) {
        const opts = {
            removeComments: true,
            minifyWhitespace: true,
            renameVariables: true,
            renameFunctions: true,
            ...options
        };

        try {
            let code = luaCode;

            // Step 1: Remove comments
            if (opts.removeComments) {
                code = this.removeComments(code);
            }

            // Step 2: Parse the code
            let ast;
            try {
                ast = luaparse.parse(code, {
                    locations: false,
                    ranges: false,
                    comments: false
                });
            } catch (parseError) {
                // If parsing fails, fall back to basic minification
                console.warn('Parse failed, using basic minification:', parseError.message);
                if (opts.minifyWhitespace) {
                    code = this.minifyWhitespace(code);
                }
                return code;
            }

            // Step 3: Rename identifiers
            if (opts.renameVariables || opts.renameFunctions) {
                ast = this.renameIdentifiers(ast);
            }

            // Step 4: Convert back to code
            try {
                code = this.astToCode(ast);
            } catch (astError) {
                console.warn('AST conversion failed, using basic minification:', astError.message);
                // Fall back to basic minification
                if (opts.minifyWhitespace) {
                    code = this.minifyWhitespace(code);
                }
            }

            // Step 5: Final whitespace minification
            if (opts.minifyWhitespace) {
                code = this.minifyWhitespace(code);
            }

            return code;

        } catch (error) {
            throw new Error(`Minification failed: ${error.message}`);
        }
    }

    /**
     * Reset internal state for new minification
     */
    reset() {
        this.variableCounter = 0;
        this.functionCounter = 0;
        this.variableMap.clear();
        this.functionMap.clear();
    }
}

module.exports = LuaMinifier;