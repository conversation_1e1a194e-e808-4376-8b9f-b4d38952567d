const luaparse = require('luaparse');

/**
 * Lua Minifier and Obfuscator for Roblox
 * Provides advanced minification and obfuscation techniques
 */
class LuaMinifier {
    constructor() {
        this.variableCounter = 0;
        this.functionCounter = 0;
        this.variableMap = new Map();
        this.functionMap = new Map();
        this.reservedWords = new Set([
            'and', 'break', 'do', 'else', 'elseif', 'end', 'false', 'for',
            'function', 'if', 'in', 'local', 'nil', 'not', 'or', 'repeat',
            'return', 'then', 'true', 'until', 'while', 'goto'
        ]);
        this.robloxGlobals = new Set([
            'game', 'workspace', 'script', 'wait', 'spawn', 'delay', 'tick',
            'print', 'warn', 'error', 'assert', 'type', 'typeof', 'getfenv',
            'setfenv', 'getmetatable', 'setmetatable', 'rawget', 'rawset',
            'rawequal', 'rawlen', 'next', 'pairs', 'ipairs', 'select',
            'tonumber', 'tostring', 'pcall', 'xpcall', 'loadstring',
            'Instance', 'Vector3', 'Vector2', 'CFrame', 'UDim2', 'Color3',
            'BrickColor', 'Ray', 'Region3', 'Enum', 'UserSettings'
        ]);
    }

    /**
     * Generate a random variable name with enhanced randomization
     */
    generateVariableName() {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const numbers = '0123456789';
        let name;
        let attempts = 0;
        const maxAttempts = 1000;

        do {
            name = '';
            // Generate 1-4 character names for better variety
            const length = Math.floor(Math.random() * 4) + 1;

            // First character must be a letter
            name += chars[Math.floor(Math.random() * chars.length)];

            // Subsequent characters can be letters or numbers
            for (let i = 1; i < length; i++) {
                const allChars = chars + numbers;
                name += allChars[Math.floor(Math.random() * allChars.length)];
            }

            attempts++;
            if (attempts > maxAttempts) {
                // Fallback to counter-based naming if we can't find a unique name
                name = `_${this.variableCounter++}`;
                break;
            }
        } while (this.isNameTaken(name));

        return name;
    }

    /**
     * Generate a random function name with enhanced randomization
     */
    generateFunctionName() {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const numbers = '0123456789';
        let name;
        let attempts = 0;
        const maxAttempts = 1000;

        do {
            name = '';
            // Generate 1-5 character function names
            const length = Math.floor(Math.random() * 5) + 1;

            // First character must be a letter
            name += chars[Math.floor(Math.random() * chars.length)];

            // Subsequent characters can be letters or numbers
            for (let i = 1; i < length; i++) {
                const allChars = chars + numbers;
                name += allChars[Math.floor(Math.random() * allChars.length)];
            }

            attempts++;
            if (attempts > maxAttempts) {
                // Fallback to counter-based naming
                name = `_f${this.functionCounter++}`;
                break;
            }
        } while (this.isNameTaken(name));

        return name;
    }

    /**
     * Check if a generated name conflicts with existing names
     */
    isNameTaken(name) {
        return this.reservedWords.has(name) ||
               this.robloxGlobals.has(name) ||
               this.isBuiltinFunction(name) ||
               Array.from(this.variableMap.values()).includes(name) ||
               Array.from(this.functionMap.values()).includes(name);
    }

    /**
     * Remove all comments from Lua code
     */
    removeComments(code) {
        // Remove single-line comments
        code = code.replace(/--(?!\[\[).*$/gm, '');

        // Remove multi-line comments
        code = code.replace(/--\[\[[\s\S]*?\]\]/g, '');

        return code;
    }

    /**
     * Minify whitespace and formatting
     */
    minifyWhitespace(code) {
        // Remove extra whitespace but preserve necessary spaces
        code = code.replace(/\s+/g, ' ');

        // Remove spaces around operators where safe
        code = code.replace(/\s*([+\-*/%=<>~!&|^(){}[\],;:])\s*/g, '$1');

        // Remove spaces after keywords where safe
        code = code.replace(/\b(local|function|if|then|else|elseif|end|for|while|do|repeat|until|return)\s+/g, '$1 ');

        // Clean up multiple spaces
        code = code.replace(/\s+/g, ' ');

        // Remove leading/trailing whitespace
        code = code.trim();

        return code;
    }

    /**
     * Comprehensive identifier renaming system
     * Renames ALL variables and functions in the code to randomized names
     */
    renameIdentifiers(ast) {
        const self = this;

        // First pass: Collect all identifiers that need renaming
        function collectIdentifiers(node, scope = 'global') {
            if (!node || typeof node !== 'object') return;

            switch (node.type) {
                case 'LocalStatement':
                    // Local variable declarations
                    if (node.variables) {
                        node.variables.forEach(variable => {
                            if (variable.type === 'Identifier' && self.shouldRename(variable.name)) {
                                const newName = self.generateVariableName();
                                self.variableMap.set(`${scope}:${variable.name}`, newName);
                            }
                        });
                    }
                    break;

                case 'AssignmentStatement':
                    // Global variable assignments
                    if (node.variables) {
                        node.variables.forEach(variable => {
                            if (variable.type === 'Identifier' && self.shouldRename(variable.name)) {
                                const key = `global:${variable.name}`;
                                if (!self.variableMap.has(key)) {
                                    const newName = self.generateVariableName();
                                    self.variableMap.set(key, newName);
                                }
                            }
                        });
                    }
                    break;

                case 'FunctionDeclaration':
                    // Function declarations
                    if (node.identifier && node.identifier.type === 'Identifier' && self.shouldRename(node.identifier.name)) {
                        const funcScope = node.isLocal ? scope : 'global';
                        const key = `${funcScope}:${node.identifier.name}`;
                        if (!self.functionMap.has(key)) {
                            const newName = self.generateFunctionName();
                            self.functionMap.set(key, newName);
                        }
                    }

                    // Function parameters
                    if (node.parameters) {
                        const funcScope = `${scope}:${node.identifier ? node.identifier.name : 'anonymous'}`;
                        node.parameters.forEach(param => {
                            if (param.type === 'Identifier' && self.shouldRename(param.name)) {
                                const newName = self.generateVariableName();
                                self.variableMap.set(`${funcScope}:${param.name}`, newName);
                            }
                        });
                    }
                    break;

                case 'ForNumericStatement':
                case 'ForGenericStatement':
                    // For loop variables
                    if (node.variable && node.variable.type === 'Identifier' && self.shouldRename(node.variable.name)) {
                        const newName = self.generateVariableName();
                        self.variableMap.set(`${scope}:${node.variable.name}`, newName);
                    }
                    if (node.variables) {
                        node.variables.forEach(variable => {
                            if (variable.type === 'Identifier' && self.shouldRename(variable.name)) {
                                const newName = self.generateVariableName();
                                self.variableMap.set(`${scope}:${variable.name}`, newName);
                            }
                        });
                    }
                    break;
            }

            // Recursively collect from child nodes
            for (const key in node) {
                if (key !== 'type' && key !== 'name') {
                    if (Array.isArray(node[key])) {
                        node[key].forEach(child => {
                            const childScope = (node.type === 'FunctionDeclaration' && node.identifier)
                                ? `${scope}:${node.identifier.name}`
                                : scope;
                            collectIdentifiers(child, childScope);
                        });
                    } else if (typeof node[key] === 'object') {
                        const childScope = (node.type === 'FunctionDeclaration' && node.identifier)
                            ? `${scope}:${node.identifier.name}`
                            : scope;
                        collectIdentifiers(node[key], childScope);
                    }
                }
            }
        }

        // Second pass: Apply the renaming
        function applyRenaming(node, scope = 'global') {
            if (!node || typeof node !== 'object') return;

            switch (node.type) {
                case 'LocalStatement':
                    if (node.variables) {
                        node.variables.forEach(variable => {
                            if (variable.type === 'Identifier') {
                                const key = `${scope}:${variable.name}`;
                                if (self.variableMap.has(key)) {
                                    variable.name = self.variableMap.get(key);
                                }
                            }
                        });
                    }
                    break;

                case 'AssignmentStatement':
                    if (node.variables) {
                        node.variables.forEach(variable => {
                            if (variable.type === 'Identifier') {
                                const key = `global:${variable.name}`;
                                if (self.variableMap.has(key)) {
                                    variable.name = self.variableMap.get(key);
                                }
                            }
                        });
                    }
                    break;

                case 'FunctionDeclaration':
                    if (node.identifier && node.identifier.type === 'Identifier') {
                        const funcScope = node.isLocal ? scope : 'global';
                        const key = `${funcScope}:${node.identifier.name}`;
                        if (self.functionMap.has(key)) {
                            node.identifier.name = self.functionMap.get(key);
                        }
                    }

                    if (node.parameters) {
                        const funcScope = `${scope}:${node.identifier ? node.identifier.name : 'anonymous'}`;
                        node.parameters.forEach(param => {
                            if (param.type === 'Identifier') {
                                const key = `${funcScope}:${param.name}`;
                                if (self.variableMap.has(key)) {
                                    param.name = self.variableMap.get(key);
                                }
                            }
                        });
                    }
                    break;

                case 'Identifier':
                    // Handle identifier references
                    const possibleKeys = [
                        `${scope}:${node.name}`,
                        `global:${node.name}`
                    ];

                    for (const key of possibleKeys) {
                        if (self.variableMap.has(key)) {
                            node.name = self.variableMap.get(key);
                            break;
                        } else if (self.functionMap.has(key)) {
                            node.name = self.functionMap.get(key);
                            break;
                        }
                    }
                    break;

                case 'ForNumericStatement':
                case 'ForGenericStatement':
                    if (node.variable && node.variable.type === 'Identifier') {
                        const key = `${scope}:${node.variable.name}`;
                        if (self.variableMap.has(key)) {
                            node.variable.name = self.variableMap.get(key);
                        }
                    }
                    if (node.variables) {
                        node.variables.forEach(variable => {
                            if (variable.type === 'Identifier') {
                                const key = `${scope}:${variable.name}`;
                                if (self.variableMap.has(key)) {
                                    variable.name = self.variableMap.get(key);
                                }
                            }
                        });
                    }
                    break;
            }

            // Recursively apply to child nodes
            for (const key in node) {
                if (key !== 'type' && key !== 'name') {
                    if (Array.isArray(node[key])) {
                        node[key].forEach(child => {
                            const childScope = (node.type === 'FunctionDeclaration' && node.identifier)
                                ? `${scope}:${node.identifier.name}`
                                : scope;
                            applyRenaming(child, childScope);
                        });
                    } else if (typeof node[key] === 'object') {
                        const childScope = (node.type === 'FunctionDeclaration' && node.identifier)
                            ? `${scope}:${node.identifier.name}`
                            : scope;
                        applyRenaming(node[key], childScope);
                    }
                }
            }
        }

        // Execute both passes
        collectIdentifiers(ast);
        applyRenaming(ast);

        return ast;
    }

    /**
     * Determine if an identifier should be renamed
     */
    shouldRename(name) {
        return !this.reservedWords.has(name) &&
               !this.robloxGlobals.has(name) &&
               !this.isBuiltinFunction(name);
    }

    /**
     * Check if a name is a built-in Lua function
     */
    isBuiltinFunction(name) {
        const builtins = [
            'print', 'warn', 'error', 'assert', 'type', 'typeof', 'getfenv', 'setfenv',
            'getmetatable', 'setmetatable', 'rawget', 'rawset', 'rawequal', 'rawlen',
            'next', 'pairs', 'ipairs', 'select', 'tonumber', 'tostring', 'pcall', 'xpcall',
            'loadstring', 'require', 'module', 'unpack', 'pack', 'math', 'string', 'table',
            'coroutine', 'io', 'os', 'debug', '_G', '_VERSION'
        ];
        return builtins.includes(name);
    }

    /**
     * Convert AST back to Lua code
     */
    astToCode(ast) {
        function nodeToString(node) {
            if (!node) return '';

            switch (node.type) {
                case 'Chunk':
                    return node.body.map(nodeToString).join(' ');

                case 'LocalStatement':
                    const vars = node.variables.map(v => nodeToString(v)).join(',');
                    const init = node.init && node.init.length > 0 ? '=' + node.init.map(nodeToString).join(',') : '';
                    return `local ${vars}${init} `;

                case 'AssignmentStatement':
                    const assignVars = node.variables.map(v => nodeToString(v)).join(',');
                    const assignInit = node.init.map(nodeToString).join(',');
                    return `${assignVars}=${assignInit} `;

                case 'FunctionDeclaration':
                    const funcType = node.isLocal ? 'local function' : 'function';
                    const name = node.identifier ? nodeToString(node.identifier) : '';
                    const params = node.parameters ? node.parameters.map(p => nodeToString(p)).join(',') : '';
                    const body = node.body.map(nodeToString).join(' ');
                    return `${funcType} ${name}(${params}) ${body}end `;

                case 'CallStatement':
                    return nodeToString(node.expression) + ' ';

                case 'CallExpression':
                    const base = nodeToString(node.base);
                    const args = node.arguments.map(nodeToString).join(',');
                    return `${base}(${args})`;

                case 'MemberExpression':
                    const memberBase = nodeToString(node.base);
                    const identifier = nodeToString(node.identifier);
                    const indexer = node.indexer === '.' ? '.' : ':';
                    return `${memberBase}${indexer}${identifier}`;

                case 'IndexExpression':
                    const indexBase = nodeToString(node.base);
                    const index = nodeToString(node.index);
                    return `${indexBase}[${index}]`;

                case 'IfStatement':
                    let ifStr = `if ${nodeToString(node.condition)} then ${node.body.map(nodeToString).join(' ')}`;
                    if (node.elseifs) {
                        node.elseifs.forEach(elseif => {
                            ifStr += `elseif ${nodeToString(elseif.condition)} then ${elseif.body.map(nodeToString).join(' ')}`;
                        });
                    }
                    if (node.else) {
                        ifStr += `else ${node.else.map(nodeToString).join(' ')}`;
                    }
                    ifStr += 'end ';
                    return ifStr;

                case 'WhileStatement':
                    return `while ${nodeToString(node.condition)} do ${node.body.map(nodeToString).join(' ')}end `;

                case 'ForNumericStatement':
                    const variable = nodeToString(node.variable);
                    const start = nodeToString(node.start);
                    const end = nodeToString(node.end);
                    const step = node.step ? ',' + nodeToString(node.step) : '';
                    const forBody = node.body.map(nodeToString).join(' ');
                    return `for ${variable}=${start},${end}${step} do ${forBody}end `;

                case 'ReturnStatement':
                    const returnArgs = node.arguments ? node.arguments.map(nodeToString).join(',') : '';
                    return `return ${returnArgs} `;

                case 'Identifier':
                    return node.name;

                case 'StringLiteral':
                    const stringValue = node.value || '';
                    return `"${stringValue.toString().replace(/"/g, '\\"')}"`;

                case 'NumericLiteral':
                    return (node.value || 0).toString();

                case 'BooleanLiteral':
                    return node.value.toString();

                case 'NilLiteral':
                    return 'nil';

                case 'BinaryExpression':
                    const left = nodeToString(node.left);
                    const right = nodeToString(node.right);
                    return `${left}${node.operator}${right}`;

                case 'UnaryExpression':
                    const argument = nodeToString(node.argument);
                    return `${node.operator}${argument}`;

                case 'TableConstructorExpression':
                    const fields = node.fields ? node.fields.map(nodeToString).join(',') : '';
                    return `{${fields}}`;

                case 'TableKey':
                    const key = nodeToString(node.key);
                    const value = nodeToString(node.value);
                    return `[${key}]=${value}`;

                case 'TableKeyString':
                    const keyStr = nodeToString(node.key);
                    const valueStr = nodeToString(node.value);
                    return `${keyStr}=${valueStr}`;

                case 'TableValue':
                    return nodeToString(node.value);

                default:
                    // For other node types, try to reconstruct basic structure
                    if (node.body && Array.isArray(node.body)) {
                        return node.body.map(nodeToString).join(' ');
                    }
                    return '';
            }
        }

        return nodeToString(ast).trim();
    }

    /**
     * Main minify function for Roblox Lua with comprehensive renaming
     */
    minify(luaCode, options = {}) {
        const opts = {
            removeComments: true,
            minifyWhitespace: true,
            renameVariables: true,
            renameFunctions: true,
            ...options
        };

        try {
            let code = luaCode;

            // Step 1: Remove comments
            if (opts.removeComments) {
                code = this.removeComments(code);
            }

            // Step 2: Comprehensive identifier renaming using regex-based approach
            if (opts.renameVariables || opts.renameFunctions) {
                code = this.performComprehensiveRenaming(code);
            }

            // Step 3: Final whitespace minification
            if (opts.minifyWhitespace) {
                code = this.minifyWhitespace(code);
            }

            return code;

        } catch (error) {
            throw new Error(`Minification failed: ${error.message}`);
        }
    }

    /**
     * Perform comprehensive renaming using regex-based approach
     * This method identifies and renames ALL user-defined variables and functions
     */
    performComprehensiveRenaming(code) {
        // Step 1: Find all identifier declarations and create mappings
        this.findAndMapIdentifiers(code);

        // Step 2: Apply the renaming using string replacement
        return this.applyRenamingToCode(code);
    }

    /**
     * Find all identifiers in the code and create rename mappings
     */
    findAndMapIdentifiers(code) {
        // Clear existing mappings
        this.variableMap.clear();
        this.functionMap.clear();

        // Process local variables
        let match;
        const localVarPattern = /local\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\s*,\s*[a-zA-Z_][a-zA-Z0-9_]*)*)/g;
        while ((match = localVarPattern.exec(code)) !== null) {
            const vars = match[1].split(',').map(v => v.trim());
            vars.forEach(varName => {
                if (this.shouldRename(varName)) {
                    this.variableMap.set(varName, this.generateVariableName());
                }
            });
        }

        // Process function declarations
        const funcPattern = /(?:local\s+)?function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g;
        while ((match = funcPattern.exec(code)) !== null) {
            const funcName = match[1];
            if (this.shouldRename(funcName)) {
                this.functionMap.set(funcName, this.generateFunctionName());
            }
        }

        // Process function parameters
        const paramPattern = /function\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\(([^)]*)\)/g;
        while ((match = paramPattern.exec(code)) !== null) {
            if (match[1].trim()) {
                const params = match[1].split(',').map(p => p.trim());
                params.forEach(param => {
                    if (param && this.shouldRename(param)) {
                        this.variableMap.set(param, this.generateVariableName());
                    }
                });
            }
        }

        // Process for loop variables
        const forPattern = /for\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\s*,\s*[a-zA-Z_][a-zA-Z0-9_]*)*)\s+(?:=|in)/g;
        while ((match = forPattern.exec(code)) !== null) {
            const vars = match[1].split(',').map(v => v.trim());
            vars.forEach(varName => {
                if (this.shouldRename(varName)) {
                    this.variableMap.set(varName, this.generateVariableName());
                }
            });
        }

        // Process global assignments (simple pattern)
        const globalPattern = /^([a-zA-Z_][a-zA-Z0-9_]*)\s*=/gm;
        while ((match = globalPattern.exec(code)) !== null) {
            const varName = match[1];
            if (this.shouldRename(varName)) {
                this.variableMap.set(varName, this.generateVariableName());
            }
        }
    }

    /**
     * Apply the renaming mappings to the code
     */
    applyRenamingToCode(code) {
        let result = code;

        // Apply function renamings first (they're usually longer and more specific)
        for (const [original, renamed] of this.functionMap.entries()) {
            // Use word boundary regex to avoid partial matches
            const regex = new RegExp(`\\b${this.escapeRegex(original)}\\b`, 'g');
            result = result.replace(regex, renamed);
        }

        // Apply variable renamings
        for (const [original, renamed] of this.variableMap.entries()) {
            // Use word boundary regex to avoid partial matches
            const regex = new RegExp(`\\b${this.escapeRegex(original)}\\b`, 'g');
            result = result.replace(regex, renamed);
        }

        return result;
    }

    /**
     * Escape special regex characters in a string
     */
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * Reset internal state for new minification
     */
    reset() {
        this.variableCounter = 0;
        this.functionCounter = 0;
        this.variableMap.clear();
        this.functionMap.clear();
    }
}

module.exports = LuaMinifier;